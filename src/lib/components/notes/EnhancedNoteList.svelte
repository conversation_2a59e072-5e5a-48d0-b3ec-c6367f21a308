<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { batchMoveNotesToFolder } from '$lib/apis/note-folders';
	import DraggableNoteItem from './DraggableNoteItem.svelte';
	import Spinner from '../common/Spinner.svelte';
	import CheckSquare from '../icons/CheckSquare.svelte';
	import Square from '../icons/Square.svelte';
	import Move from '../icons/Move.svelte';

	const dispatch = createEventDispatcher();

	export let filteredNotes: any = {};
	export let selectedNote: any = null;
	export let loaded: boolean = false;
	export let folders: any[] = [];

	let selectedNotes: Set<string> = new Set();
	let multiSelectMode: boolean = false;
	let showBatchActions: boolean = false;
	let showFolderSelector: boolean = false;

	$: showBatchActions = selectedNotes.size > 0;

	const toggleMultiSelectMode = () => {
		multiSelectMode = !multiSelectMode;
		if (!multiSelectMode) {
			selectedNotes.clear();
			selectedNotes = selectedNotes;
		}
	};

	const toggleNoteSelection = (event: CustomEvent) => {
		const note = event.detail.note;
		if (selectedNotes.has(note.id)) {
			selectedNotes.delete(note.id);
		} else {
			selectedNotes.add(note.id);
		}
		selectedNotes = selectedNotes;
	};

	const selectAllNotes = () => {
		selectedNotes.clear();
		Object.keys(filteredNotes).forEach((timeRange) => {
			if (filteredNotes[timeRange] && Array.isArray(filteredNotes[timeRange])) {
				filteredNotes[timeRange].forEach((note: any) => {
					if (note && note.id) {
						selectedNotes.add(note.id);
					}
				});
			}
		});
		selectedNotes = selectedNotes;
	};

	const clearSelection = () => {
		selectedNotes.clear();
		selectedNotes = selectedNotes;
	};

	const batchMoveToFolder = async (folderId: string | null) => {
		if (selectedNotes.size === 0) return;

		try {
			const noteIds = Array.from(selectedNotes);
			const result = await batchMoveNotesToFolder(
				localStorage.token,
				noteIds,
				folderId || undefined
			);

			if (result.failed_notes && result.failed_notes.length > 0) {
				toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個失敗`);
			} else {
				const folderName = folderId
					? folders.find((f) => f.id === folderId)?.name || '未知資料夾'
					: '根目錄';
				toast.success(`已將 ${result.moved_notes.length} 個筆記移動到 ${folderName}`);
			}

			// 清除選擇並重新載入
			clearSelection();
			dispatch('notesChanged');
		} catch (error) {
			toast.error(`批量移動失敗: ${error}`);
		}

		showFolderSelector = false;
	};

	const handleNoteSelect = (event: CustomEvent) => {
		if (!multiSelectMode) {
			dispatch('noteSelected', event.detail);
		}
	};

	const getTotalSelectedCount = () => {
		return selectedNotes.size;
	};
</script>

<div class="note-list-container">
	<!-- 批量操作工具欄 -->
	{#if showBatchActions}
		<div
			class="batch-toolbar bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 p-3"
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-3">
					<span class="text-sm font-medium text-blue-700 dark:text-blue-300">
						已選擇 {getTotalSelectedCount()} 個筆記
					</span>
					<button
						type="button"
						class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
						on:click={clearSelection}
					>
						清除選擇
					</button>
				</div>

				<div class="flex items-center space-x-2">
					<button
						type="button"
						class="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
						on:click={() => (showFolderSelector = !showFolderSelector)}
					>
						<Move class="w-4 h-4 mr-1" />
						移動到
					</button>
				</div>
			</div>

			<!-- 資料夾選擇器 -->
			{#if showFolderSelector}
				<div
					class="mt-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"
				>
					<div class="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
						<!-- 根目錄選項 -->
						<button
							type="button"
							class="text-left p-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
							on:click={() => batchMoveToFolder(null)}
						>
							📁 根目錄
						</button>

						<!-- 資料夾選項 -->
						{#each folders as folder (folder.id)}
							<button
								type="button"
								class="text-left p-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded truncate"
								on:click={() => batchMoveToFolder(folder.id)}
								title={folder.name}
							>
								📁 {folder.name}
							</button>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}

	<!-- 多選模式切換按鈕 -->
	<div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
		<h3 class="text-sm font-medium text-gray-900 dark:text-white">筆記列表</h3>
		<div class="flex items-center space-x-2">
			{#if multiSelectMode}
				<button
					type="button"
					class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
					on:click={selectAllNotes}
				>
					全選
				</button>
			{/if}
			<button
				type="button"
				class="flex items-center px-2 py-1 text-xs {multiSelectMode
					? 'bg-blue-600 text-white'
					: 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'} rounded transition-colors"
				on:click={toggleMultiSelectMode}
			>
				{#if multiSelectMode}
					<CheckSquare class="w-3 h-3 mr-1" />
					退出多選
				{:else}
					<Square class="w-3 h-3 mr-1" />
					多選模式
				{/if}
			</button>
		</div>
	</div>

	<!-- 筆記列表 -->
	<div class="overflow-auto flex-1">
		{#if !loaded}
			<div class="flex justify-center items-center h-40">
				<Spinner />
			</div>
		{:else if Object.keys(filteredNotes).length === 0}
			<div class="flex flex-col items-center justify-center h-32 text-center px-4">
				<p class="text-gray-500 dark:text-gray-400 text-sm">沒有找到筆記</p>
			</div>
		{:else}
			<ul class="divide-y divide-gray-200 dark:divide-gray-700">
				{#each Object.keys(filteredNotes) as timeRange}
					{#if filteredNotes[timeRange] && Array.isArray(filteredNotes[timeRange])}
						{#each filteredNotes[timeRange] as note (note.id)}
							{#if note && typeof note === 'object' && 'id' in note}
								<DraggableNoteItem
									{note}
									selected={note.id === selectedNote?.id}
									{multiSelectMode}
									isSelected={selectedNotes.has(note.id)}
									on:select={handleNoteSelect}
									on:toggleSelect={toggleNoteSelection}
									on:dragSingle
									on:dragMultiple
								/>
							{/if}
						{/each}
					{/if}
				{/each}
			</ul>
		{/if}
	</div>
</div>

<style>
	.note-list-container {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.batch-toolbar {
		flex-shrink: 0;
	}
</style>
