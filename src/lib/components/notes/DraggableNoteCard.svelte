<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	
	const dispatch = createEventDispatcher();
	
	export let note: any;
	export let multiSelectMode: boolean = false;
	export let isSelected: boolean = false;
	
	let isDragging = false;
	
	const handleDragStart = (e: DragEvent) => {
		if (!e.dataTransfer) return;
		
		isDragging = true;
		e.dataTransfer.effectAllowed = 'move';
		e.dataTransfer.setData('application/json', JSON.stringify({
			type: 'note',
			noteId: note.id,
			noteTitle: note.title
		}));
		
		dispatch('dragStart', { note });
	};
	
	const handleDragEnd = () => {
		isDragging = false;
	};
	
	const handleClick = (e: MouseEvent) => {
		if (multiSelectMode) {
			e.preventDefault();
			dispatch('toggleSelect', { note });
		}
	};
	
	const handleCheckboxChange = (e: Event) => {
		e.stopPropagation();
		dispatch('toggleSelect', { note });
	};
</script>

<div 
	class="note-card {isDragging ? 'dragging' : ''} {isSelected ? 'selected' : ''}"
	draggable="true"
	on:dragstart={handleDragStart}
	on:dragend={handleDragEnd}
	on:click={handleClick}
	role="button"
	tabindex="0"
>
	<!-- 多選模式的複選框 -->
	{#if multiSelectMode}
		<div class="absolute top-2 left-2 z-10">
			<input
				type="checkbox"
				checked={isSelected}
				on:change={handleCheckboxChange}
				class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
			/>
		</div>
	{/if}
	
	<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all cursor-pointer relative {multiSelectMode ? 'pt-8' : ''}">
		{#if !multiSelectMode}
			<a href={`/notes/${note.id}`} class="block">
				<div class="flex items-start justify-between mb-2">
					<h3 class="font-medium text-gray-900 dark:text-white line-clamp-1">
						{note.title}
					</h3>
					<span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
						{new Date(note.updated_at / 1000000).toLocaleDateString()}
					</span>
				</div>
				{#if note.data?.content?.md}
					<p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
						{note.data.content.md.substring(0, 150)}
						{note.data.content.md.length > 150 ? '...' : ''}
					</p>
				{/if}
			</a>
		{:else}
			<div class="block">
				<div class="flex items-start justify-between mb-2">
					<h3 class="font-medium text-gray-900 dark:text-white line-clamp-1">
						{note.title}
					</h3>
					<span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
						{new Date(note.updated_at / 1000000).toLocaleDateString()}
					</span>
				</div>
				{#if note.data?.content?.md}
					<p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
						{note.data.content.md.substring(0, 150)}
						{note.data.content.md.length > 150 ? '...' : ''}
					</p>
				{/if}
			</div>
		{/if}
	</div>
</div>

<style>
	.note-card.dragging {
		opacity: 0.5;
		transform: rotate(2deg) scale(0.95);
	}
	
	.note-card.selected {
		background-color: rgba(59, 130, 246, 0.1);
		border-radius: 8px;
		border: 2px solid #3b82f6;
	}
	
	.note-card {
		transition: all 0.2s ease;
		position: relative;
	}
	
	.note-card:hover:not(.dragging) {
		transform: translateY(-2px);
	}
	
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
	
	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
