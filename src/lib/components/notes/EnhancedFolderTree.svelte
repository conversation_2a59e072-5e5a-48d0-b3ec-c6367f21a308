<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { getNoteFolderTree, getSharedNoteFolderTree } from '$lib/apis/note-folders';
	import { toast } from 'svelte-sonner';
	import DroppableRootArea from './DroppableRootArea.svelte';
	import DroppableFolderNode from './DroppableFolderNode.svelte';
	import Spinner from '../common/Spinner.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Users from '../icons/Users.svelte';
	
	const dispatch = createEventDispatcher();
	
	export let selectedFolderId: string | null = null;
	export let selectedRootType: string = 'owned';
	export let selectedNotes: Set<string> = new Set();
	
	let folderTree: any[] = [];
	let sharedFolderTree: any[] = [];
	let expandedFolders: Set<string> = new Set(['root', 'shared']);
	let loading = false;
	let rootNoteCount = 0;
	
	// 初始化資料夾樹
	const init = async () => {
		loading = true;
		try {
			// 同時載入用戶擁有的資料夾和共用資料夾
			const [ownedFolders, sharedFolders] = await Promise.all([
				getNoteFolderTree(localStorage.token),
				getSharedNoteFolderTree(localStorage.token)
			]);
			folderTree = ownedFolders;
			sharedFolderTree = sharedFolders;

			// 默認展開 Root 節點
			expandedFolders.add('root');
			expandedFolders = expandedFolders;
		} catch (error) {
			toast.error(`${error}`);
		} finally {
			loading = false;
		}
	};

	// 切換資料夾展開狀態
	const toggleFolder = (folderId: string) => {
		if (expandedFolders.has(folderId)) {
			expandedFolders.delete(folderId);
		} else {
			expandedFolders.add(folderId);
		}
		expandedFolders = expandedFolders;
	};

	// 處理資料夾選擇
	const handleFolderSelect = (event: CustomEvent) => {
		const { folderId, rootType } = event.detail;
		selectedFolderId = folderId;
		if (rootType) {
			selectedRootType = rootType;
		}
		dispatch('folderSelected', { folderId, rootType });
	};

	// 處理筆記移動完成
	const handleNotesMoved = (event: CustomEvent) => {
		dispatch('notesMoved', event.detail);
	};

	// 遞歸渲染資料夾節點
	const renderFolderNode = (folder: any, level: number = 0) => {
		const isExpanded = expandedFolders.has(folder.id);
		const isSelected = selectedFolderId === folder.id;
		
		return {
			folder,
			level,
			isExpanded,
			isSelected
		};
	};

	// 組件掛載時初始化
	init();
</script>

<div class="folder-tree h-full flex flex-col">
	{#if loading}
		<div class="flex justify-center items-center h-32">
			<Spinner />
		</div>
	{:else}
		<div class="flex-1 overflow-auto">
			<!-- 我的筆記區域 -->
			<div class="mb-4">
				<div class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
					<button
						type="button"
						class="flex items-center w-full"
						on:click={() => toggleFolder('root')}
					>
						<ChevronRight 
							class="w-4 h-4 mr-1 transform transition-transform {expandedFolders.has('root') ? 'rotate-90' : ''}" 
						/>
						我的筆記
					</button>
				</div>

				{#if expandedFolders.has('root')}
					<!-- 根目錄 -->
					<DroppableRootArea
						isSelected={selectedFolderId === null && selectedRootType === 'owned'}
						{selectedNotes}
						noteCount={rootNoteCount}
						on:select={handleFolderSelect}
						on:notesMoved={handleNotesMoved}
					/>

					<!-- 用戶資料夾 -->
					{#if folderTree.length === 0}
						<div class="px-8 py-4 text-center text-gray-500 dark:text-gray-400 text-sm">
							尚無資料夾
						</div>
					{:else}
						{#each folderTree as folder (folder.id)}
							<DroppableFolderNode
								{folder}
								level={1}
								isExpanded={expandedFolders.has(folder.id)}
								isSelected={selectedFolderId === folder.id}
								{selectedNotes}
								on:select={handleFolderSelect}
								on:toggle={(e) => toggleFolder(e.detail.folderId)}
								on:notesMoved={handleNotesMoved}
							/>
						{/each}
					{/if}
				{/if}
			</div>

			<!-- 共用筆記區域 -->
			{#if sharedFolderTree.length > 0}
				<div class="mb-4">
					<div class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
						<button
							type="button"
							class="flex items-center w-full"
							on:click={() => toggleFolder('shared')}
						>
							<ChevronRight 
								class="w-4 h-4 mr-1 transform transition-transform {expandedFolders.has('shared') ? 'rotate-90' : ''}" 
							/>
							<Users class="w-4 h-4 mr-2" />
							共用筆記
						</button>
					</div>

					{#if expandedFolders.has('shared')}
						<!-- 共用根目錄 -->
						<div class="px-8">
							<button
								type="button"
								class="w-full flex items-center py-2 px-3 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md {selectedFolderId === null && selectedRootType === 'shared' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300'}"
								on:click={() => handleFolderSelect({ detail: { folderId: null, rootType: 'shared' } })}
							>
								📁 共用根目錄
							</button>
						</div>

						<!-- 共用資料夾 -->
						{#each sharedFolderTree as folder (folder.id)}
							<DroppableFolderNode
								{folder}
								level={1}
								isExpanded={expandedFolders.has(folder.id)}
								isSelected={selectedFolderId === folder.id}
								{selectedNotes}
								on:select={handleFolderSelect}
								on:toggle={(e) => toggleFolder(e.detail.folderId)}
								on:notesMoved={handleNotesMoved}
							/>
						{/each}
					{/if}
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.folder-tree {
		background-color: #fafafa;
	}
	
	.dark .folder-tree {
		background-color: #1f2937;
	}
</style>
