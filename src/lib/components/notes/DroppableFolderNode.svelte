<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { moveNoteToFolder, batchMoveNotesToFolder } from '$lib/apis/note-folders';
	import { toast } from 'svelte-sonner';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Folder from '../icons/Folder.svelte';
	import FolderOpen from '../icons/FolderOpen.svelte';
	
	const dispatch = createEventDispatcher();
	
	export let folder: any;
	export let level: number = 0;
	export let isExpanded: boolean = false;
	export let isSelected: boolean = false;
	export let selectedNotes: Set<string> = new Set();
	
	let isDropTarget = false;
	let draggedNotes: any[] = [];
	
	const handleDragOver = (e: DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer) {
			e.dataTransfer.dropEffect = 'move';
		}
		isDropTarget = true;
	};
	
	const handleDragLeave = (e: DragEvent) => {
		// 只有當滑鼠真正離開元素時才取消高亮
		const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
		const x = e.clientX;
		const y = e.clientY;
		
		if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
			isDropTarget = false;
		}
	};
	
	const handleDrop = async (e: DragEvent) => {
		e.preventDefault();
		isDropTarget = false;
		
		if (!e.dataTransfer) return;
		
		try {
			const data = JSON.parse(e.dataTransfer.getData('application/json'));
			if (data.type === 'note') {
				// 檢查是否是批量移動
				if (selectedNotes.size > 1 && selectedNotes.has(data.noteId)) {
					// 批量移動
					const noteIds = Array.from(selectedNotes);
					const result = await batchMoveNotesToFolder(localStorage.token, noteIds, folder.id);
					
					if (result.failed_notes && result.failed_notes.length > 0) {
						toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個失敗`);
					} else {
						toast.success(`已將 ${result.moved_notes.length} 個筆記移動到 "${folder.name}"`);
					}
				} else {
					// 單個移動
					await moveNoteToFolder(localStorage.token, data.noteId, folder.id);
					toast.success(`筆記 "${data.noteTitle}" 已移動到 "${folder.name}"`);
				}
				
				// 觸發重新載入
				dispatch('notesMoved', { targetFolderId: folder.id });
			}
		} catch (error) {
			toast.error(`移動失敗: ${error}`);
		}
	};
	
	const handleFolderClick = () => {
		dispatch('select', { folderId: folder.id });
	};
	
	const handleToggle = () => {
		dispatch('toggle', { folderId: folder.id });
	};
</script>

<div 
	class="folder-node {isDropTarget ? 'drop-target' : ''}"
	style="padding-left: {level * 16}px"
	on:dragover={handleDragOver}
	on:dragleave={handleDragLeave}
	on:drop={handleDrop}
	role="treeitem"
	tabindex="0"
>
	<div class="flex items-center py-2 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md group">
		<!-- 展開/收起按鈕 -->
		{#if folder.children && folder.children.length > 0}
			<button
				type="button"
				class="flex-shrink-0 w-4 h-4 mr-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				on:click={handleToggle}
			>
				<ChevronRight class="w-4 h-4 transform transition-transform {isExpanded ? 'rotate-90' : ''}" />
			</button>
		{:else}
			<div class="w-4 h-4 mr-1"></div>
		{/if}
		
		<!-- 資料夾圖標 -->
		<div class="flex-shrink-0 w-4 h-4 mr-2 text-gray-500 dark:text-gray-400">
			{#if isExpanded}
				<FolderOpen class="w-4 h-4" />
			{:else}
				<Folder class="w-4 h-4" />
			{/if}
		</div>
		
		<!-- 資料夾名稱 -->
		<button
			type="button"
			class="flex-1 text-left text-sm {isSelected
				? 'text-blue-700 dark:text-blue-300 font-medium'
				: 'text-gray-700 dark:text-gray-300'} truncate"
			on:click={handleFolderClick}
		>
			{folder.name}
		</button>
		
		<!-- 筆記數量 -->
		{#if folder.total_note_count > 0}
			<span class="text-xs text-gray-400 mr-2 flex-shrink-0">
				{folder.total_note_count}
			</span>
		{/if}
	</div>
	
	<!-- 子資料夾 -->
	{#if isExpanded && folder.children && folder.children.length > 0}
		{#each folder.children as childFolder (childFolder.id)}
			<svelte:self
				folder={childFolder}
				level={level + 1}
				isExpanded={false}
				{isSelected}
				{selectedNotes}
				on:select
				on:toggle
				on:notesMoved
			/>
		{/each}
	{/if}
</div>

<style>
	.folder-node.drop-target {
		background-color: rgba(59, 130, 246, 0.1);
		border: 2px dashed #3b82f6;
		border-radius: 6px;
	}
	
	.folder-node {
		transition: all 0.2s ease;
	}
	
	.folder-node:hover {
		background-color: rgba(0, 0, 0, 0.02);
	}
	
	.dark .folder-node:hover {
		background-color: rgba(255, 255, 255, 0.02);
	}
</style>
