<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { moveNoteToFolder, batchMoveNotesToFolder } from '$lib/apis/note-folders';
	import { toast } from 'svelte-sonner';
	import Home from '../icons/Home.svelte';
	
	const dispatch = createEventDispatcher();
	
	export let isSelected: boolean = false;
	export let selectedNotes: Set<string> = new Set();
	export let noteCount: number = 0;
	
	let isDropTarget = false;
	
	const handleDragOver = (e: DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer) {
			e.dataTransfer.dropEffect = 'move';
		}
		isDropTarget = true;
	};
	
	const handleDragLeave = (e: DragEvent) => {
		const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
		const x = e.clientX;
		const y = e.clientY;
		
		if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
			isDropTarget = false;
		}
	};
	
	const handleDrop = async (e: DragEvent) => {
		e.preventDefault();
		isDropTarget = false;
		
		if (!e.dataTransfer) return;
		
		try {
			const data = JSON.parse(e.dataTransfer.getData('application/json'));
			if (data.type === 'note') {
				// 檢查是否是批量移動
				if (selectedNotes.size > 1 && selectedNotes.has(data.noteId)) {
					// 批量移動到根目錄
					const noteIds = Array.from(selectedNotes);
					const result = await batchMoveNotesToFolder(localStorage.token, noteIds, null);
					
					if (result.failed_notes && result.failed_notes.length > 0) {
						toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個失敗`);
					} else {
						toast.success(`已將 ${result.moved_notes.length} 個筆記移動到根目錄`);
					}
				} else {
					// 單個移動到根目錄
					await moveNoteToFolder(localStorage.token, data.noteId, null);
					toast.success(`筆記 "${data.noteTitle}" 已移動到根目錄`);
				}
				
				// 觸發重新載入
				dispatch('notesMoved', { targetFolderId: null });
			}
		} catch (error) {
			toast.error(`移動失敗: ${error}`);
		}
	};
	
	const handleRootClick = () => {
		dispatch('select', { folderId: null, rootType: 'owned' });
	};
</script>

<div 
	class="root-area {isDropTarget ? 'drop-target' : ''} {isSelected ? 'selected' : ''}"
	on:dragover={handleDragOver}
	on:dragleave={handleDragLeave}
	on:drop={handleDrop}
	role="button"
	tabindex="0"
>
	<button
		type="button"
		class="w-full flex items-center py-3 px-4 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
		on:click={handleRootClick}
	>
		<!-- 根目錄圖標 -->
		<div class="flex-shrink-0 w-4 h-4 mr-3 text-gray-500 dark:text-gray-400">
			<Home class="w-4 h-4" />
		</div>
		
		<!-- 根目錄標籤 -->
		<span class="flex-1 text-left text-sm {isSelected
			? 'text-blue-700 dark:text-blue-300 font-medium'
			: 'text-gray-700 dark:text-gray-300'}">
			根目錄
		</span>
		
		<!-- 筆記數量 -->
		{#if noteCount > 0}
			<span class="text-xs text-gray-400 mr-2 flex-shrink-0">
				{noteCount}
			</span>
		{/if}
	</button>
</div>

<style>
	.root-area.drop-target {
		background-color: rgba(59, 130, 246, 0.1);
		border: 2px dashed #3b82f6;
		border-radius: 6px;
	}
	
	.root-area.selected {
		background-color: rgba(59, 130, 246, 0.1);
		border-left: 3px solid #3b82f6;
	}
	
	.root-area {
		transition: all 0.2s ease;
		margin: 4px 8px;
	}
</style>
