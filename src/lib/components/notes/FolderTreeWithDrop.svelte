<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { moveNoteToFolder, batchMoveNotesToFolder } from '$lib/apis/note-folders';
	import { toast } from 'svelte-sonner';
	import FolderTree from './FolderTree.svelte';

	const dispatch = createEventDispatcher();

	export let selectedFolderId: string | null = null;
	export let selectedRootType: string = 'owned';
	export let expandedFolders: Set<string> = new Set();
	export let selectedNotes: Set<string> = new Set();

	let dropTargets: Map<string, boolean> = new Map();

	// 處理拖拽到根目錄
	const handleRootDragOver = (e: DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer) {
			e.dataTransfer.dropEffect = 'move';
		}
		dropTargets.set('root', true);
		dropTargets = dropTargets;
	};

	const handleRootDragLeave = () => {
		dropTargets.set('root', false);
		dropTargets = dropTargets;
	};

	const handleRootDrop = async (e: DragEvent) => {
		e.preventDefault();
		dropTargets.set('root', false);
		dropTargets = dropTargets;

		if (!e.dataTransfer) return;

		try {
			const data = JSON.parse(e.dataTransfer.getData('application/json'));
			if (data.type === 'note') {
				// 檢查是否是批量移動
				if (selectedNotes.size > 1 && selectedNotes.has(data.noteId)) {
					// 批量移動到根目錄
					const noteIds = Array.from(selectedNotes);
					const result = await batchMoveNotesToFolder(localStorage.token, noteIds, undefined);

					if (result.failed_notes && result.failed_notes.length > 0) {
						toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個失敗`);
					} else {
						toast.success(`已將 ${result.moved_notes.length} 個筆記移動到根目錄`);
					}
				} else {
					// 單個移動到根目錄
					await moveNoteToFolder(localStorage.token, data.noteId, undefined);
					toast.success(`筆記 "${data.noteTitle}" 已移動到根目錄`);
				}

				// 觸發重新載入
				dispatch('notesMoved');
			}
		} catch (error) {
			toast.error(`移動失敗: ${error}`);
		}
	};

	// 處理資料夾選擇
	const handleFolderSelected = (event: CustomEvent) => {
		dispatch('folderSelected', event.detail);
	};

	// 處理資料夾拖拽
	const handleFolderDragOver = (folderId: string) => (e: DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer) {
			e.dataTransfer.dropEffect = 'move';
		}
		dropTargets.set(folderId, true);
		dropTargets = dropTargets;
	};

	const handleFolderDragLeave = (folderId: string) => () => {
		dropTargets.set(folderId, false);
		dropTargets = dropTargets;
	};

	const handleFolderDrop = (folderId: string) => async (e: DragEvent) => {
		e.preventDefault();
		dropTargets.set(folderId, false);
		dropTargets = dropTargets;

		if (!e.dataTransfer) return;

		try {
			const data = JSON.parse(e.dataTransfer.getData('application/json'));
			if (data.type === 'note') {
				// 檢查是否是批量移動
				if (selectedNotes.size > 1 && selectedNotes.has(data.noteId)) {
					// 批量移動到資料夾
					const noteIds = Array.from(selectedNotes);
					const result = await batchMoveNotesToFolder(localStorage.token, noteIds, folderId);

					if (result.failed_notes && result.failed_notes.length > 0) {
						toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個失敗`);
					} else {
						toast.success(`已將 ${result.moved_notes.length} 個筆記移動到資料夾`);
					}
				} else {
					// 單個移動到資料夾
					await moveNoteToFolder(localStorage.token, data.noteId, folderId);
					toast.success(`筆記 "${data.noteTitle}" 已移動到資料夾`);
				}

				// 觸發重新載入
				dispatch('notesMoved');
			}
		} catch (error) {
			toast.error(`移動失敗: ${error}`);
		}
	};
</script>

<div class="folder-tree-container h-full">
	<!-- 包裝原有的 FolderTree -->
	<div class="h-full" style="position: relative;">
		<FolderTree
			bind:selectedFolderId
			bind:selectedRootType
			bind:expandedFolders
			on:folderSelected={handleFolderSelected}
		/>

		<!-- 拖拽覆蓋層 -->
		<div class="drag-overlay" class:active={Array.from(dropTargets.values()).some(Boolean)}>
			<!-- 根目錄拖拽區域 -->
			<div
				class="drop-zone root-drop-zone {dropTargets.get('root') ? 'highlight' : ''}"
				on:dragover={handleRootDragOver}
				on:dragleave={handleRootDragLeave}
				on:drop={handleRootDrop}
				role="button"
				tabindex="0"
			>
				<div class="drop-indicator">📁 移動到根目錄</div>
			</div>
		</div>
	</div>
</div>

<style>
	.folder-tree-container {
		position: relative;
	}

	.drag-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 10;
		opacity: 0;
		transition: opacity 0.2s ease;
	}

	.drag-overlay.active {
		opacity: 1;
		pointer-events: auto;
	}

	.drop-zone {
		position: absolute;
		pointer-events: auto;
		border-radius: 6px;
		transition: all 0.2s ease;
	}

	.root-drop-zone {
		top: 60px; /* 調整到根目錄位置 */
		left: 8px;
		right: 8px;
		height: 40px;
		background-color: transparent;
	}

	.drop-zone.highlight {
		background-color: rgba(59, 130, 246, 0.1);
		border: 2px dashed #3b82f6;
	}

	.drop-indicator {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		font-size: 14px;
		font-weight: 500;
		color: #3b82f6;
		opacity: 0;
		transition: opacity 0.2s ease;
	}

	.drop-zone.highlight .drop-indicator {
		opacity: 1;
	}
</style>
