"""
測試筆記移動功能的 API 端點
"""
import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# 假設這些是實際的導入路徑
# from open_webui.main import app
# from open_webui.models.notes import Notes
# from open_webui.models.note_folders import NoteFolders


class TestNoteMoveAPI:
    """測試筆記移動 API"""
    
    def setup_method(self):
        """測試前的設置"""
        # self.client = TestClient(app)
        self.test_user_id = "test_user_123"
        self.test_note_id = "note_123"
        self.test_folder_id = "folder_123"
        
    def test_move_note_to_folder_success(self):
        """測試成功移動筆記到資料夾"""
        # 模擬請求數據
        move_data = {
            "folder_id": self.test_folder_id
        }
        
        # 模擬認證用戶
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            # 模擬筆記存在且用戶有權限
            with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                mock_note = Mock(
                    id=self.test_note_id,
                    user_id=self.test_user_id,
                    access_control=None
                )
                mock_get_note.return_value = mock_note
                
                # 模擬資料夾存在且用戶有權限
                with patch('open_webui.models.note_folders.NoteFolders.get_folder_by_id') as mock_get_folder:
                    mock_folder = Mock(id=self.test_folder_id, user_id=self.test_user_id)
                    mock_get_folder.return_value = mock_folder
                    
                    # 模擬更新成功
                    with patch('open_webui.models.notes.Notes.update_note_by_id') as mock_update:
                        mock_update.return_value = mock_note
                        
                        # 執行測試
                        # response = self.client.post(
                        #     f"/api/v1/notes/{self.test_note_id}/move",
                        #     json=move_data,
                        #     headers={"Authorization": "Bearer test_token"}
                        # )
                        
                        # 驗證結果
                        # assert response.status_code == 200
                        # assert response.json()["id"] == self.test_note_id
                        
                        # 驗證調用了正確的方法
                        mock_get_note.assert_called_once_with(self.test_note_id)
                        mock_get_folder.assert_called_once_with(self.test_folder_id, self.test_user_id)
                        mock_update.assert_called_once()
    
    def test_move_note_to_root_success(self):
        """測試成功移動筆記到根目錄"""
        move_data = {
            "folder_id": None
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                mock_note = Mock(
                    id=self.test_note_id,
                    user_id=self.test_user_id,
                    access_control=None
                )
                mock_get_note.return_value = mock_note
                
                with patch('open_webui.models.notes.Notes.update_note_by_id') as mock_update:
                    mock_update.return_value = mock_note
                    
                    # 移動到根目錄不需要檢查資料夾權限
                    # response = self.client.post(
                    #     f"/api/v1/notes/{self.test_note_id}/move",
                    #     json=move_data,
                    #     headers={"Authorization": "Bearer test_token"}
                    # )
                    
                    # assert response.status_code == 200
                    mock_update.assert_called_once()
    
    def test_move_note_permission_denied(self):
        """測試沒有權限移動筆記"""
        move_data = {
            "folder_id": self.test_folder_id
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id="other_user", role="user")
            
            with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                mock_note = Mock(
                    id=self.test_note_id,
                    user_id=self.test_user_id,  # 不同的用戶
                    access_control={}  # 私有筆記
                )
                mock_get_note.return_value = mock_note
                
                # response = self.client.post(
                #     f"/api/v1/notes/{self.test_note_id}/move",
                #     json=move_data,
                #     headers={"Authorization": "Bearer test_token"}
                # )
                
                # assert response.status_code == 403
    
    def test_move_note_folder_not_found(self):
        """測試目標資料夾不存在"""
        move_data = {
            "folder_id": "nonexistent_folder"
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                mock_note = Mock(
                    id=self.test_note_id,
                    user_id=self.test_user_id,
                    access_control=None
                )
                mock_get_note.return_value = mock_note
                
                with patch('open_webui.models.note_folders.NoteFolders.get_folder_by_id') as mock_get_folder:
                    mock_get_folder.return_value = None
                    
                    with patch('open_webui.models.note_folders.NoteFolders.get_folder_by_id_with_access_check') as mock_check:
                        mock_check.return_value = None
                        
                        # response = self.client.post(
                        #     f"/api/v1/notes/{self.test_note_id}/move",
                        #     json=move_data,
                        #     headers={"Authorization": "Bearer test_token"}
                        # )
                        
                        # assert response.status_code == 404


class TestBatchMoveAPI:
    """測試批量移動 API"""
    
    def setup_method(self):
        """測試前的設置"""
        # self.client = TestClient(app)
        self.test_user_id = "test_user_123"
        self.test_note_ids = ["note_1", "note_2", "note_3"]
        self.test_folder_id = "folder_123"
    
    def test_batch_move_success(self):
        """測試成功批量移動筆記"""
        move_data = {
            "note_ids": self.test_note_ids,
            "folder_id": self.test_folder_id
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            # 模擬資料夾存在
            with patch('open_webui.models.note_folders.NoteFolders.get_folder_by_id') as mock_get_folder:
                mock_folder = Mock(id=self.test_folder_id, user_id=self.test_user_id)
                mock_get_folder.return_value = mock_folder
                
                # 模擬所有筆記都存在且有權限
                with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                    def mock_note_side_effect(note_id):
                        return Mock(
                            id=note_id,
                            user_id=self.test_user_id,
                            access_control=None
                        )
                    mock_get_note.side_effect = mock_note_side_effect
                    
                    # 模擬更新成功
                    with patch('open_webui.models.notes.Notes.update_note_by_id') as mock_update:
                        def mock_update_side_effect(note_id, form_data):
                            return Mock(id=note_id)
                        mock_update.side_effect = mock_update_side_effect
                        
                        # response = self.client.post(
                        #     "/api/v1/notes/batch/move",
                        #     json=move_data,
                        #     headers={"Authorization": "Bearer test_token"}
                        # )
                        
                        # assert response.status_code == 200
                        # result = response.json()
                        # assert len(result["moved_notes"]) == 3
                        # assert len(result["failed_notes"]) == 0
    
    def test_batch_move_too_many_notes(self):
        """測試批量移動筆記數量過多"""
        move_data = {
            "note_ids": [f"note_{i}" for i in range(101)],  # 超過限制
            "folder_id": self.test_folder_id
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            # response = self.client.post(
            #     "/api/v1/notes/batch/move",
            #     json=move_data,
            #     headers={"Authorization": "Bearer test_token"}
            # )
            
            # assert response.status_code == 400
            # assert "Too many notes" in response.json()["detail"]
    
    def test_batch_move_partial_success(self):
        """測試部分成功的批量移動"""
        move_data = {
            "note_ids": self.test_note_ids,
            "folder_id": self.test_folder_id
        }
        
        with patch('open_webui.utils.auth.get_verified_user') as mock_auth:
            mock_auth.return_value = Mock(id=self.test_user_id, role="user")
            
            with patch('open_webui.models.note_folders.NoteFolders.get_folder_by_id') as mock_get_folder:
                mock_folder = Mock(id=self.test_folder_id, user_id=self.test_user_id)
                mock_get_folder.return_value = mock_folder
                
                # 模擬部分筆記不存在或沒有權限
                with patch('open_webui.models.notes.Notes.get_note_by_id') as mock_get_note:
                    def mock_note_side_effect(note_id):
                        if note_id == "note_2":
                            return None  # 筆記不存在
                        return Mock(
                            id=note_id,
                            user_id=self.test_user_id if note_id != "note_3" else "other_user",
                            access_control=None
                        )
                    mock_get_note.side_effect = mock_note_side_effect
                    
                    with patch('open_webui.models.notes.Notes.update_note_by_id') as mock_update:
                        def mock_update_side_effect(note_id, form_data):
                            return Mock(id=note_id)
                        mock_update.side_effect = mock_update_side_effect
                        
                        # response = self.client.post(
                        #     "/api/v1/notes/batch/move",
                        #     json=move_data,
                        #     headers={"Authorization": "Bearer test_token"}
                        # )
                        
                        # assert response.status_code == 200
                        # result = response.json()
                        # assert len(result["moved_notes"]) == 1  # 只有 note_1 成功
                        # assert len(result["failed_notes"]) == 2  # note_2 和 note_3 失敗


if __name__ == "__main__":
    # 運行測試
    # pytest.main([__file__])
    pass
