import json
import logging
from typing import Optional, List


from fastapi import APIRouter, Depends, HTTPException, Request, status, BackgroundTasks
from pydantic import BaseModel

from open_webui.socket.main import sio


from open_webui.models.users import Users, UserResponse
from open_webui.models.notes import Notes, NoteModel, NoteForm, NoteUserResponse
from open_webui.models.note_folders import NoteFolders, NoteFolderModel

from open_webui.config import ENABLE_ADMIN_CHAT_ACCESS, ENABLE_ADMIN_EXPORT
from open_webui.constants import ERROR_MESSAGES
from open_webui.env import SRC_LOG_LEVELS


from open_webui.utils.auth import get_admin_user, get_verified_user
from open_webui.utils.access_control import has_access, has_permission

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

router = APIRouter()

############################
# GetNotes
############################


@router.get("/", response_model=list[NoteUserResponse])
async def get_notes(request: Request, user=Depends(get_verified_user)):

    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    notes = [
        NoteUserResponse(
            **{
                **note.model_dump(),
                "user": UserResponse(**Users.get_user_by_id(note.user_id).model_dump()),
            }
        )
        for note in Notes.get_accessible_notes_by_user_id(user.id)
    ]

    return notes


class NoteTitleIdResponse(BaseModel):
    id: str
    title: str
    updated_at: int
    created_at: int


@router.get("/list", response_model=list[NoteTitleIdResponse])
async def get_note_list(request: Request, user=Depends(get_verified_user)):

    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    notes = [
        NoteTitleIdResponse(**note.model_dump())
        for note in Notes.get_accessible_notes_by_user_id(user.id)
    ]

    return notes


############################
# CreateNewNote
############################


@router.post("/create", response_model=Optional[NoteModel])
async def create_new_note(
    request: Request, form_data: NoteForm, user=Depends(get_verified_user)
):

    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        note = Notes.insert_new_note(form_data, user.id)
        return note
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# GetNoteById
############################


@router.get("/{id}", response_model=Optional[NoteModel])
async def get_note_by_id(request: Request, id: str, user=Depends(get_verified_user)):
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    note = Notes.get_note_by_id(id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=ERROR_MESSAGES.NOT_FOUND
        )

    # 檢查用戶是否有權限訪問這個筆記
    if user.role != "admin":
        has_note_access = False

        # 1. 用戶擁有筆記
        if user.id == note.user_id:
            has_note_access = True
        # 2. 筆記是公開的（access_control 為 None 或空字典）
        elif note.access_control is None or note.access_control == {}:
            # 如果筆記在資料夾中，檢查資料夾權限
            if note.folder_id:
                # 檢查用戶是否有資料夾權限
                folder = NoteFolders.get_folder_by_id(note.folder_id, user.id)
                if folder:  # 用戶擁有資料夾
                    has_note_access = True
                else:  # 檢查分享權限
                    folder = NoteFolders.get_folder_by_id_with_access_check(
                        note.folder_id, user.id
                    )
                    if folder:
                        has_note_access = True
            else:
                # 筆記不在資料夾中且是公開的
                has_note_access = True
        # 3. 筆記有明確的讀取權限
        elif has_access(user.id, type="read", access_control=note.access_control):
            has_note_access = True

        if not has_note_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail=ERROR_MESSAGES.DEFAULT()
            )

    return note


############################
# UpdateNoteById
############################


@router.post("/{id}/update", response_model=Optional[NoteModel])
async def update_note_by_id(
    request: Request, id: str, form_data: NoteForm, user=Depends(get_verified_user)
):
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    note = Notes.get_note_by_id(id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=ERROR_MESSAGES.NOT_FOUND
        )

    if user.role != "admin" and (
        user.id != note.user_id
        and not has_access(user.id, type="write", access_control=note.access_control)
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail=ERROR_MESSAGES.DEFAULT()
        )

    try:
        note = Notes.update_note_by_id(id, form_data)
        await sio.emit(
            "note-events",
            note.model_dump(),
            to=f"note:{note.id}",
        )

        return note
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# DeleteNoteById
############################


@router.delete("/{id}/delete", response_model=bool)
async def delete_note_by_id(request: Request, id: str, user=Depends(get_verified_user)):
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    note = Notes.get_note_by_id(id)
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=ERROR_MESSAGES.NOT_FOUND
        )

    if user.role != "admin" and (
        user.id != note.user_id
        and not has_access(user.id, type="write", access_control=note.access_control)
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail=ERROR_MESSAGES.DEFAULT()
        )

    try:
        note = Notes.delete_note_by_id(id)
        return True
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# Folder Related APIs
############################


class NoteMoveForm(BaseModel):
    folder_id: Optional[str] = None


class NoteBatchMoveForm(BaseModel):
    note_ids: List[str]
    folder_id: Optional[str] = None


@router.post("/{note_id}/move", response_model=Optional[NoteModel])
async def move_note_to_folder(
    request: Request,
    note_id: str,
    form_data: NoteMoveForm,
    user=Depends(get_verified_user),
):
    """移動筆記到指定資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        # 1. 檢查筆記是否存在及用戶權限
        note = Notes.get_note_by_id(note_id)
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Note not found"
            )

        # 2. 檢查用戶是否有權限修改該筆記
        if user.role != "admin" and (
            user.id != note.user_id
            and not has_access(
                user.id, type="write", access_control=note.access_control
            )
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="No permission to modify this note",
            )

        # 3. 檢查目標資料夾權限
        if form_data.folder_id:
            # 首先檢查是否為資料夾擁有者
            folder = NoteFolders.get_folder_by_id(form_data.folder_id, user.id)
            if not folder:
                # 檢查共用資料夾的讀取權限
                folder = NoteFolders.get_folder_by_id_with_access_check(
                    form_data.folder_id, user.id
                )
                if not folder:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND, detail="Folder not found"
                    )

                # 檢查是否有寫入權限
                if not has_access(
                    user.id, type="write", access_control=folder.access_control
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="No write permission to target folder",
                    )

        # 4. 執行移動操作
        from open_webui.models.notes import NoteUpdateForm

        update_form = NoteUpdateForm(folder_id=form_data.folder_id)
        updated_note = Notes.update_note_by_id(note_id, update_form)

        if not updated_note:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update note",
            )

        return updated_note
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


@router.get("/folders/{folder_id}/notes", response_model=List[NoteUserResponse])
async def get_notes_in_folder(
    request: Request,
    folder_id: str,
    user=Depends(get_verified_user),
):
    """獲取指定資料夾中的筆記"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    try:
        # 首先檢查資料夾是否屬於當前用戶
        folder = NoteFolders.get_folder_by_id(folder_id, user.id)

        # 如果不屬於用戶，檢查是否有分享權限
        if not folder:
            folder = NoteFolders.get_folder_by_id_with_access_check(folder_id, user.id)
            if not folder:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Folder not found"
                )

        # 獲取資料夾中的筆記（包含權限檢查）
        notes = Notes.get_accessible_notes_by_folder_id(folder_id, user.id)

        return [
            NoteUserResponse(
                **{
                    **note.model_dump(),
                    "user": UserResponse(
                        **Users.get_user_by_id(note.user_id).model_dump()
                    ),
                }
            )
            for note in notes
        ]
    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )


############################
# BatchMoveNotes
############################


class NoteBatchMoveResponse(BaseModel):
    moved_notes: List[NoteModel]
    failed_notes: List[dict]


@router.post("/batch/move", response_model=NoteBatchMoveResponse)
async def batch_move_notes_to_folder(
    request: Request,
    form_data: NoteBatchMoveForm,
    user=Depends(get_verified_user),
):
    """批量移動筆記到指定資料夾"""
    if user.role != "admin" and not has_permission(
        user.id, "features.notes", request.app.state.config.USER_PERMISSIONS
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED,
        )

    if len(form_data.note_ids) > 100:  # 限制批量操作數量
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Too many notes to move at once (max 100)",
        )

    try:
        # 檢查目標資料夾權限（一次性檢查）
        target_folder = None
        if form_data.folder_id:
            target_folder = NoteFolders.get_folder_by_id(form_data.folder_id, user.id)
            if not target_folder:
                target_folder = NoteFolders.get_folder_by_id_with_access_check(
                    form_data.folder_id, user.id
                )
                if not target_folder:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Target folder not found",
                    )

                if not has_access(
                    user.id, type="write", access_control=target_folder.access_control
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="No write permission to target folder",
                    )

        moved_notes = []
        failed_notes = []

        for note_id in form_data.note_ids:
            try:
                # 檢查筆記權限
                note = Notes.get_note_by_id(note_id)
                if not note:
                    failed_notes.append({"note_id": note_id, "error": "Note not found"})
                    continue

                if user.role != "admin" and (
                    user.id != note.user_id
                    and not has_access(
                        user.id, type="write", access_control=note.access_control
                    )
                ):
                    failed_notes.append({"note_id": note_id, "error": "No permission"})
                    continue

                # 執行移動
                from open_webui.models.notes import NoteUpdateForm

                update_form = NoteUpdateForm(folder_id=form_data.folder_id)
                updated_note = Notes.update_note_by_id(note_id, update_form)
                if updated_note:
                    moved_notes.append(updated_note)
                else:
                    failed_notes.append({"note_id": note_id, "error": "Update failed"})

            except Exception as e:
                failed_notes.append({"note_id": note_id, "error": str(e)})

        return NoteBatchMoveResponse(moved_notes=moved_notes, failed_notes=failed_notes)

    except HTTPException:
        raise
    except Exception as e:
        log.exception(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=ERROR_MESSAGES.DEFAULT()
        )
