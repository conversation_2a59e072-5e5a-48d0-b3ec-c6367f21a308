import json
import time
import uuid
from typing import Optional

from open_webui.internal.db import Base, get_db
from open_webui.utils.access_control import has_access
from open_webui.models.users import Users, UserResponse


from pydantic import BaseModel, ConfigDict
from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    String,
    Text,
    JSON,
    ForeignKey,
    Index,
)
from sqlalchemy import or_, func, select, and_, text
from sqlalchemy.sql import exists

####################
# Note DB Schema
####################


class Note(Base):
    __tablename__ = "note"

    id = Column(Text, primary_key=True)
    user_id = Column(Text)

    # 資料夾關聯字段
    folder_id = Column(String, ForeignKey("note_folder.id"), nullable=True, index=True)

    title = Column(Text)
    data = Column(JSON, nullable=True)
    meta = Column(JSON, nullable=True)

    access_control = Column(JSON, nullable=True)

    created_at = Column(BigInteger)
    updated_at = Column(BigInteger)

    # 添加索引以提高查詢性能
    __table_args__ = (Index("idx_note_user_folder", "user_id", "folder_id"),)


class NoteModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    user_id: str

    # 資料夾關聯字段
    folder_id: Optional[str] = None

    title: str
    data: Optional[dict] = None
    meta: Optional[dict] = None

    access_control: Optional[dict] = None

    created_at: int  # timestamp in epoch
    updated_at: int  # timestamp in epoch


####################
# Forms
####################


class NoteForm(BaseModel):
    title: str
    data: Optional[dict] = None
    meta: Optional[dict] = None
    access_control: Optional[dict] = None
    folder_id: Optional[str] = None


class NoteUpdateForm(BaseModel):
    title: Optional[str] = None
    data: Optional[dict] = None
    meta: Optional[dict] = None
    access_control: Optional[dict] = None
    folder_id: Optional[str] = None


class NoteUserResponse(NoteModel):
    user: Optional[UserResponse] = None


class NoteTable:
    def insert_new_note(
        self,
        form_data: NoteForm,
        user_id: str,
    ) -> Optional[NoteModel]:
        with get_db() as db:
            note = NoteModel(
                **{
                    "id": str(uuid.uuid4()),
                    "user_id": user_id,
                    **form_data.model_dump(),
                    "created_at": int(time.time_ns()),
                    "updated_at": int(time.time_ns()),
                }
            )

            new_note = Note(**note.model_dump())

            db.add(new_note)
            db.commit()
            return note

    def get_notes(self) -> list[NoteModel]:
        with get_db() as db:
            notes = db.query(Note).order_by(Note.updated_at.desc()).all()
            return [NoteModel.model_validate(note) for note in notes]

    def get_notes_by_user_id(
        self, user_id: str, permission: str = "write"
    ) -> list[NoteModel]:
        notes = self.get_notes()
        return [
            note
            for note in notes
            if note.user_id == user_id
            or has_access(user_id, permission, note.access_control)
        ]

    def get_accessible_notes_by_user_id(self, user_id: str) -> list[NoteModel]:
        """
        Get all notes accessible to a user, including:
        - Notes owned by the user
        - Public notes (access_control is None)
        - Notes with explicit read/write permissions for the user
        """
        notes = self.get_notes()
        accessible_notes = []

        for note in notes:
            # User owns the note
            if note.user_id == user_id:
                accessible_notes.append(note)
            # Public note (access_control is None)
            elif note.access_control is None:
                accessible_notes.append(note)
            # Note with explicit permissions
            elif has_access(user_id, "read", note.access_control):
                accessible_notes.append(note)

        return accessible_notes

    def get_note_by_id(self, id: str) -> Optional[NoteModel]:
        with get_db() as db:
            note = db.query(Note).filter(Note.id == id).first()
            return NoteModel.model_validate(note) if note else None

    def update_note_by_id(
        self, id: str, form_data: NoteUpdateForm
    ) -> Optional[NoteModel]:
        with get_db() as db:
            note = db.query(Note).filter(Note.id == id).first()
            if not note:
                return None

            form_data = form_data.model_dump(exclude_unset=True)

            if "title" in form_data:
                note.title = form_data["title"]
            if "data" in form_data and form_data["data"] is not None:
                note.data = {**(note.data or {}), **form_data["data"]}
            if "meta" in form_data and form_data["meta"] is not None:
                note.meta = {**(note.meta or {}), **form_data["meta"]}

            if "access_control" in form_data:
                note.access_control = form_data["access_control"]

            if "folder_id" in form_data:
                note.folder_id = form_data["folder_id"]

            note.updated_at = int(time.time_ns())

            db.commit()
            return NoteModel.model_validate(note) if note else None

    def delete_note_by_id(self, id: str):
        with get_db() as db:
            db.query(Note).filter(Note.id == id).delete()
            db.commit()
            return True

    def get_notes_by_folder_id(
        self, folder_id: Optional[str], user_id: Optional[str]
    ) -> list[NoteModel]:
        """根據資料夾ID獲取筆記列表"""
        with get_db() as db:
            query = db.query(Note)

            # 如果指定了 user_id，則過濾用戶
            if user_id is not None:
                query = query.filter(Note.user_id == user_id)

            if folder_id is None:
                # 獲取沒有資料夾的筆記（根目錄）
                query = query.filter(Note.folder_id.is_(None))
            else:
                # 獲取指定資料夾中的筆記
                query = query.filter(Note.folder_id == folder_id)

            notes = query.order_by(Note.updated_at.desc()).all()
            return [NoteModel.model_validate(note) for note in notes]

    def get_accessible_notes_by_folder_id(
        self, folder_id: str, user_id: str
    ) -> list[NoteModel]:
        """獲取指定資料夾中用戶有權限訪問的筆記"""
        from open_webui.models.note_folders import NoteFolders

        with get_db() as db:
            # 首先檢查用戶對資料夾的權限
            folder = NoteFolders.get_folder_by_id(folder_id, user_id)
            is_folder_owner = folder is not None

            # 如果不是資料夾擁有者，檢查是否有分享權限
            if not is_folder_owner:
                folder = NoteFolders.get_folder_by_id_with_access_check(
                    folder_id, user_id
                )
                if not folder:
                    return []  # 沒有資料夾權限，返回空列表

            # 獲取資料夾中的所有筆記
            notes = (
                db.query(Note)
                .filter(Note.folder_id == folder_id)
                .order_by(Note.updated_at.desc())
                .all()
            )

            accessible_notes = []
            for note in notes:
                # 如果用戶是資料夾擁有者，可以看到所有筆記（除非筆記有明確的限制權限）
                if is_folder_owner:
                    # 資料夾擁有者可以看到所有筆記，除非筆記明確拒絕訪問
                    if (
                        note.access_control is None
                        or note.access_control == {}
                        or note.user_id == user_id
                        or has_access(user_id, "read", note.access_control)
                    ):
                        accessible_notes.append(NoteModel.model_validate(note))
                else:
                    # 如果用戶不是資料夾擁有者，但有資料夾分享權限
                    # 可以看到公開筆記和有明確權限的筆記
                    if note.user_id == user_id:
                        # 用戶自己的筆記
                        accessible_notes.append(NoteModel.model_validate(note))
                    elif note.access_control is None or note.access_control == {}:
                        # 公開筆記（繼承資料夾權限）
                        accessible_notes.append(NoteModel.model_validate(note))
                    elif has_access(user_id, "read", note.access_control):
                        # 有明確讀取權限的筆記
                        accessible_notes.append(NoteModel.model_validate(note))

            return accessible_notes

    def get_notes_by_user_id_with_folder_info(self, user_id: str) -> list[dict]:
        """獲取用戶的所有筆記，包含資料夾信息"""
        with get_db() as db:
            from open_webui.models.note_folders import NoteFolder

            # 使用左連接獲取筆記和資料夾信息
            query = (
                db.query(Note, NoteFolder)
                .outerjoin(NoteFolder, Note.folder_id == NoteFolder.id)
                .filter(Note.user_id == user_id)
                .order_by(Note.updated_at.desc())
            )

            results = []
            for note, folder in query.all():
                note_dict = NoteModel.model_validate(note).model_dump()
                if folder:
                    note_dict["folder"] = {
                        "id": folder.id,
                        "name": folder.name,
                        "color": folder.color,
                    }
                else:
                    note_dict["folder"] = None
                results.append(note_dict)

            return results

    def search_notes_in_folder(
        self, user_id: str, query: str, folder_id: Optional[str] = None
    ) -> list[NoteModel]:
        """在指定資料夾中搜索筆記"""
        with get_db() as db:
            search_query = db.query(Note).filter(Note.user_id == user_id)

            # 資料夾過濾
            if folder_id is None:
                search_query = search_query.filter(Note.folder_id.is_(None))
            else:
                search_query = search_query.filter(Note.folder_id == folder_id)

            # 文本搜索
            if query:
                search_query = search_query.filter(
                    or_(Note.title.contains(query), Note.data.contains(query))
                )

            notes = search_query.order_by(Note.updated_at.desc()).all()
            return [NoteModel.model_validate(note) for note in notes]


Notes = NoteTable()
