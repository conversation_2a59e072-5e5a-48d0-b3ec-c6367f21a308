# 筆記移動功能整合指南

本指南將幫助您將新實現的筆記移動功能整合到現有的 open-webui 筆記系統中。

## 整合方案概覽

我們提供了三種整合方案，您可以根據需要選擇：

1. **方案一：最小整合** - 只添加基本的拖拽功能
2. **方案二：完整整合** - 替換現有組件，獲得完整功能
3. **方案三：漸進式整合** - 逐步添加功能，保持向後兼容

## 方案一：最小整合（推薦）

這是最簡單的整合方案，只需要對現有代碼進行最小修改。

### 步驟 1: 添加拖拽到現有筆記卡片

修改 `src/lib/components/notes/NotesWithFolders.svelte`：

```svelte
<!-- 在現有的筆記卡片中添加拖拽功能 -->
{#each filteredNotes[timeRange] as note (note.id)}
  <div
    class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
    draggable="true"
    on:dragstart={(e) => handleNoteDragStart(e, note)}
    on:dragend={handleNoteDragEnd}
  >
    <a href={`/notes/${note.id}`} class="block">
      <!-- 現有的筆記內容 -->
    </a>
  </div>
{/each}
```

### 步驟 2: 添加拖拽處理函數

在 `NotesWithFolders.svelte` 的 script 部分添加：

```javascript
import { moveNoteToFolder } from '$lib/apis/note-folders';
import { toast } from 'svelte-sonner';

let isDragging = false;

const handleNoteDragStart = (e, note) => {
  if (!e.dataTransfer) return;
  
  isDragging = true;
  e.dataTransfer.effectAllowed = 'move';
  e.dataTransfer.setData('application/json', JSON.stringify({
    type: 'note',
    noteId: note.id,
    noteTitle: note.title
  }));
};

const handleNoteDragEnd = () => {
  isDragging = false;
};

// 添加到資料夾樹的拖拽接收
const handleFolderDrop = async (e, folderId = null) => {
  e.preventDefault();
  
  if (!e.dataTransfer) return;
  
  try {
    const data = JSON.parse(e.dataTransfer.getData('application/json'));
    if (data.type === 'note') {
      await moveNoteToFolder(localStorage.token, data.noteId, folderId);
      const folderName = folderId ? '資料夾' : '根目錄';
      toast.success(`筆記已移動到${folderName}`);
      await loadNotes(); // 重新載入筆記
    }
  } catch (error) {
    toast.error(`移動失敗: ${error}`);
  }
};
```

### 步驟 3: 修改資料夾樹以接收拖拽

在資料夾樹的每個節點上添加拖拽接收：

```svelte
<!-- 在 FolderTree.svelte 或相關組件中 -->
<div 
  class="folder-item"
  on:dragover={(e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }}
  on:drop={(e) => handleFolderDrop(e, folder.id)}
>
  <!-- 現有的資料夾內容 -->
</div>

<!-- 根目錄拖拽區域 -->
<div 
  class="root-folder"
  on:dragover={(e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }}
  on:drop={(e) => handleFolderDrop(e, null)}
>
  根目錄
</div>
```

## 方案二：完整整合

如果您想要完整的功能，包括批量選擇和移動，可以使用我們創建的新組件。

### 步驟 1: 替換主要組件

```svelte
<!-- 在 NotesWithFolders.svelte 中 -->
<script>
  import EnhancedNoteList from './EnhancedNoteList.svelte';
  import EnhancedFolderTree from './EnhancedFolderTree.svelte';
</script>

<!-- 替換資料夾樹 -->
<EnhancedFolderTree
  bind:selectedFolderId
  bind:selectedRootType
  selectedNotes={new Set()}
  on:folderSelected={handleFolderSelected}
  on:notesMoved={handleNotesChanged}
/>

<!-- 替換筆記列表 -->
<EnhancedNoteList
  {filteredNotes}
  {selectedNote}
  {loaded}
  {folders}
  on:noteSelected={handleNoteSelected}
  on:notesChanged={handleNotesChanged}
/>
```

### 步驟 2: 添加必要的處理函數

```javascript
let selectedNotes = new Set();

const handleNotesChanged = async () => {
  await loadNotes();
};

const handleNoteSelected = (event) => {
  // 處理筆記選擇
  goto(`/notes/${event.detail.note.id}`);
};
```

## 方案三：漸進式整合

這個方案允許您逐步添加功能，而不破壞現有的用戶體驗。

### 階段 1: 添加基本拖拽

按照方案一的步驟實現基本拖拽功能。

### 階段 2: 添加批量選擇

```svelte
<!-- 添加多選模式切換按鈕 -->
<button
  class="multi-select-toggle"
  on:click={() => multiSelectMode = !multiSelectMode}
>
  {multiSelectMode ? '退出多選' : '多選模式'}
</button>

<!-- 在筆記卡片中添加複選框 -->
{#if multiSelectMode}
  <input
    type="checkbox"
    checked={selectedNotes.has(note.id)}
    on:change={() => toggleNoteSelection(note.id)}
  />
{/if}
```

### 階段 3: 添加批量操作

```svelte
<!-- 批量操作工具欄 -->
{#if selectedNotes.size > 0}
  <div class="batch-toolbar">
    <span>已選擇 {selectedNotes.size} 個筆記</span>
    <button on:click={() => batchMoveToFolder(null)}>
      移動到根目錄
    </button>
  </div>
{/if}
```

## 樣式調整

添加必要的 CSS 樣式：

```css
/* 拖拽狀態 */
.note-card.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

/* 拖拽目標高亮 */
.folder-item.drop-target {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
}

/* 批量選擇 */
.note-card.selected {
  border: 2px solid #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

/* 批量操作工具欄 */
.batch-toolbar {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  padding: 12px;
  border-radius: 6px;
}
```

## 測試建議

### 功能測試

1. **基本拖拽**：
   - 拖拽筆記到不同資料夾
   - 拖拽筆記到根目錄
   - 測試權限控制

2. **批量操作**：
   - 選擇多個筆記
   - 批量移動到資料夾
   - 測試部分失敗情況

3. **用戶體驗**：
   - 視覺反饋是否清晰
   - 錯誤提示是否友好
   - 操作是否直觀

### 性能測試

1. 測試大量筆記時的拖拽性能
2. 測試批量操作的響應時間
3. 檢查記憶體使用情況

## 故障排除

### 常見問題

1. **拖拽不工作**：
   - 檢查 `draggable="true"` 屬性
   - 確認事件處理器正確綁定
   - 檢查瀏覽器控制台錯誤

2. **權限錯誤**：
   - 確認 API 端點正確實現
   - 檢查用戶權限設置
   - 查看網路請求日誌

3. **樣式問題**：
   - 檢查 CSS 類名是否正確
   - 確認深色模式適配
   - 測試不同螢幕尺寸

### 調試技巧

```javascript
// 啟用拖拽調試
const handleNoteDragStart = (e, note) => {
  console.log('Drag started:', note.id);
  // ... 其他邏輯
};

const handleFolderDrop = async (e, folderId) => {
  console.log('Drop on folder:', folderId);
  // ... 其他邏輯
};
```

## 下一步

1. 選擇適合的整合方案
2. 按步驟實施功能
3. 進行充分測試
4. 收集用戶反饋
5. 持續優化改進

如果在整合過程中遇到問題，請參考：
- [API 文檔](../api/note_move_api.md)
- [組件使用指南](../frontend/note_move_components.md)
- [測試指南](../testing/api_testing.md)
