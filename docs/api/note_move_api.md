# 筆記移動 API 文檔

本文檔描述了筆記移動功能的 API 端點，包括單個移動和批量移動功能。

## 目錄

- [單個筆記移動](#單個筆記移動)
- [批量筆記移動](#批量筆記移動)
- [權限控制](#權限控制)
- [錯誤處理](#錯誤處理)
- [使用示例](#使用示例)

## 單個筆記移動

### 移動筆記到資料夾

**端點**: `POST /api/v1/notes/{note_id}/move`

**描述**: 將指定的筆記移動到目標資料夾或根目錄

**路徑參數**:
- `note_id` (string): 要移動的筆記 ID

**請求體**:
```json
{
  "folder_id": "string | null"
}
```

**參數說明**:
- `folder_id`: 目標資料夾 ID，設為 `null` 表示移動到根目錄

**響應**:
```json
{
  "id": "string",
  "user_id": "string",
  "folder_id": "string | null",
  "title": "string",
  "data": "object",
  "meta": "object",
  "access_control": "object | null",
  "created_at": "number",
  "updated_at": "number"
}
```

**狀態碼**:
- `200`: 移動成功
- `401`: 未授權
- `403`: 權限不足
- `404`: 筆記或資料夾不存在
- `500`: 服務器錯誤

## 批量筆記移動

### 批量移動筆記到資料夾

**端點**: `POST /api/v1/notes/batch/move`

**描述**: 批量移動多個筆記到目標資料夾或根目錄

**請求體**:
```json
{
  "note_ids": ["string"],
  "folder_id": "string | null"
}
```

**參數說明**:
- `note_ids`: 要移動的筆記 ID 數組（最多 100 個）
- `folder_id`: 目標資料夾 ID，設為 `null` 表示移動到根目錄

**響應**:
```json
{
  "moved_notes": [
    {
      "id": "string",
      "user_id": "string",
      "folder_id": "string | null",
      "title": "string",
      "data": "object",
      "meta": "object",
      "access_control": "object | null",
      "created_at": "number",
      "updated_at": "number"
    }
  ],
  "failed_notes": [
    {
      "note_id": "string",
      "error": "string"
    }
  ]
}
```

**狀態碼**:
- `200`: 請求處理完成（可能部分成功）
- `400`: 請求參數錯誤（如筆記數量過多）
- `401`: 未授權
- `403`: 權限不足
- `404`: 目標資料夾不存在

## 權限控制

### 筆記修改權限

用戶需要滿足以下條件之一才能移動筆記：

1. **筆記擁有者**: 用戶是筆記的創建者
2. **明確寫入權限**: 筆記的 `access_control` 中明確授予用戶寫入權限
3. **管理員**: 用戶具有管理員角色

### 目標資料夾權限

移動到資料夾時，用戶需要滿足以下條件之一：

1. **資料夾擁有者**: 用戶是資料夾的創建者
2. **共用資料夾寫入權限**: 資料夾的 `access_control` 中授予用戶寫入權限
3. **移動到根目錄**: 不需要資料夾權限

### 權限檢查流程

```mermaid
graph TD
    A[開始移動] --> B[檢查筆記是否存在]
    B --> C[檢查筆記修改權限]
    C --> D{目標是根目錄?}
    D -->|是| E[執行移動]
    D -->|否| F[檢查資料夾是否存在]
    F --> G[檢查資料夾寫入權限]
    G --> E[執行移動]
    E --> H[返回結果]
```

## 錯誤處理

### 常見錯誤

1. **筆記不存在**
   ```json
   {
     "detail": "Note not found"
   }
   ```

2. **權限不足**
   ```json
   {
     "detail": "No permission to modify this note"
   }
   ```

3. **資料夾不存在**
   ```json
   {
     "detail": "Folder not found"
   }
   ```

4. **資料夾寫入權限不足**
   ```json
   {
     "detail": "No write permission to target folder"
   }
   ```

5. **批量操作數量過多**
   ```json
   {
     "detail": "Too many notes to move at once (max 100)"
   }
   ```

### 批量操作錯誤處理

批量移動操作採用部分成功模式：
- 成功移動的筆記會在 `moved_notes` 中返回
- 失敗的筆記會在 `failed_notes` 中返回，包含具體錯誤信息
- 即使部分筆記移動失敗，HTTP 狀態碼仍為 200

## 使用示例

### JavaScript/TypeScript

```typescript
// 移動單個筆記到資料夾
async function moveNoteToFolder(noteId: string, folderId: string | null) {
  const response = await fetch(`/api/v1/notes/${noteId}/move`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ folder_id: folderId })
  });
  
  if (!response.ok) {
    throw new Error(`移動失敗: ${response.statusText}`);
  }
  
  return response.json();
}

// 批量移動筆記
async function batchMoveNotes(noteIds: string[], folderId: string | null) {
  const response = await fetch('/api/v1/notes/batch/move', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ 
      note_ids: noteIds, 
      folder_id: folderId 
    })
  });
  
  if (!response.ok) {
    throw new Error(`批量移動失敗: ${response.statusText}`);
  }
  
  const result = await response.json();
  
  if (result.failed_notes.length > 0) {
    console.warn(`部分筆記移動失敗:`, result.failed_notes);
  }
  
  return result;
}
```

### Python

```python
import requests

def move_note_to_folder(note_id: str, folder_id: str = None, token: str = None):
    """移動筆記到資料夾"""
    url = f"/api/v1/notes/{note_id}/move"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    data = {"folder_id": folder_id}
    
    response = requests.post(url, json=data, headers=headers)
    response.raise_for_status()
    return response.json()

def batch_move_notes(note_ids: list, folder_id: str = None, token: str = None):
    """批量移動筆記"""
    url = "/api/v1/notes/batch/move"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    data = {
        "note_ids": note_ids,
        "folder_id": folder_id
    }
    
    response = requests.post(url, json=data, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    
    if result["failed_notes"]:
        print(f"部分筆記移動失敗: {len(result['failed_notes'])} 個")
    
    return result
```

## 最佳實踐

1. **批量操作**: 對於多個筆記的移動，優先使用批量 API 以提高性能
2. **錯誤處理**: 批量操作時要檢查 `failed_notes` 並適當處理失敗情況
3. **權限檢查**: 在前端也要進行基本的權限檢查，提供更好的用戶體驗
4. **進度反饋**: 對於大量筆記的移動，考慮提供進度反饋
5. **事務性**: 批量操作不是原子性的，部分失敗是正常情況

## 相關文檔

- [前端組件使用指南](../frontend/note_move_components.md)
- [權限控制詳解](../security/access_control.md)
- [API 測試指南](../testing/api_testing.md)
