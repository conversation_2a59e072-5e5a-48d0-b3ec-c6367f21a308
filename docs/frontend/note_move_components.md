# 筆記移動前端組件使用指南

本文檔介紹了筆記移動功能的前端組件使用方法，包括拖拽、批量選擇和移動等功能。

## 目錄

- [組件概覽](#組件概覽)
- [拖拽功能](#拖拽功能)
- [批量選擇](#批量選擇)
- [組件集成](#組件集成)
- [自定義樣式](#自定義樣式)
- [事件處理](#事件處理)

## 組件概覽

### 核心組件

1. **DraggableNoteItem**: 可拖拽的筆記列表項
2. **DroppableFolderNode**: 可接收拖拽的資料夾節點
3. **DroppableRootArea**: 可接收拖拽的根目錄區域
4. **EnhancedNoteList**: 增強的筆記列表（支援批量選擇）
5. **EnhancedFolderTree**: 增強的資料夾樹（支援拖拽接收）

### 組件關係

```mermaid
graph TD
    A[EnhancedNoteList] --> B[DraggableNoteItem]
    C[EnhancedFolderTree] --> D[DroppableFolderNode]
    C --> E[DroppableRootArea]
    B -.->|拖拽| D
    B -.->|拖拽| E
```

## 拖拽功能

### DraggableNoteItem 組件

**用途**: 使筆記列表項可拖拽

**屬性**:
```typescript
export let note: any;                    // 筆記對象
export let selected: boolean = false;    // 是否選中
export let multiSelectMode: boolean = false;  // 多選模式
export let isSelected: boolean = false;  // 是否被選中（多選）
```

**事件**:
- `select`: 選擇筆記
- `toggleSelect`: 切換選擇狀態（多選模式）
- `dragSingle`: 開始拖拽單個筆記
- `dragMultiple`: 開始拖拽多個筆記

**使用示例**:
```svelte
<DraggableNoteItem
  {note}
  selected={note.id === selectedNote?.id}
  {multiSelectMode}
  isSelected={selectedNotes.has(note.id)}
  on:select={handleNoteSelect}
  on:toggleSelect={handleToggleSelect}
  on:dragSingle={handleDragSingle}
  on:dragMultiple={handleDragMultiple}
/>
```

### DroppableFolderNode 組件

**用途**: 使資料夾節點可接收拖拽

**屬性**:
```typescript
export let folder: any;                  // 資料夾對象
export let level: number = 0;            // 層級深度
export let isExpanded: boolean = false;  // 是否展開
export let isSelected: boolean = false;  // 是否選中
export let selectedNotes: Set<string> = new Set();  // 選中的筆記
```

**事件**:
- `select`: 選擇資料夾
- `toggle`: 切換展開狀態
- `notesMoved`: 筆記移動完成

**使用示例**:
```svelte
<DroppableFolderNode
  {folder}
  {level}
  {isExpanded}
  {isSelected}
  {selectedNotes}
  on:select={handleFolderSelect}
  on:toggle={handleToggle}
  on:notesMoved={handleNotesMoved}
/>
```

### 拖拽數據格式

拖拽時傳遞的數據格式：
```json
{
  "type": "note",
  "noteId": "note_123",
  "noteTitle": "筆記標題"
}
```

## 批量選擇

### EnhancedNoteList 組件

**用途**: 提供批量選擇和移動功能的筆記列表

**屬性**:
```typescript
export let filteredNotes: any = {};      // 過濾後的筆記
export let selectedNote: any = null;     // 當前選中的筆記
export let loaded: boolean = false;      // 是否已載入
export let folders: any[] = [];          // 資料夾列表
```

**功能特性**:
- 多選模式切換
- 全選/清除選擇
- 批量移動到資料夾
- 拖拽支援

**使用示例**:
```svelte
<EnhancedNoteList
  {filteredNotes}
  {selectedNote}
  {loaded}
  {folders}
  on:noteSelected={handleNoteSelected}
  on:notesChanged={handleNotesChanged}
/>
```

### 批量操作工具欄

當有筆記被選中時，會顯示批量操作工具欄：

```svelte
<!-- 批量操作工具欄 -->
{#if showBatchActions}
  <div class="batch-toolbar">
    <span>已選擇 {selectedCount} 個筆記</span>
    <button on:click={clearSelection}>清除選擇</button>
    <button on:click={showFolderSelector}>移動到</button>
  </div>
{/if}
```

## 組件集成

### 完整的筆記管理頁面

```svelte
<script lang="ts">
  import { PaneGroup, Pane, PaneResizer } from 'paneforge';
  import EnhancedFolderTree from './EnhancedFolderTree.svelte';
  import EnhancedNoteList from './EnhancedNoteList.svelte';
  
  let selectedFolderId: string | null = null;
  let selectedRootType: string = 'owned';
  let selectedNotes: Set<string> = new Set();
  let notes: any = {};
  let folders: any[] = [];
  
  const handleFolderSelected = (event) => {
    selectedFolderId = event.detail.folderId;
    selectedRootType = event.detail.rootType || 'owned';
    // 載入該資料夾的筆記
    loadNotesInFolder(selectedFolderId);
  };
  
  const handleNotesMoved = () => {
    // 重新載入筆記列表
    loadNotesInFolder(selectedFolderId);
  };
</script>

<div class="h-full">
  <PaneGroup direction="horizontal">
    <!-- 資料夾樹 -->
    <Pane defaultSize={30} minSize={15} maxSize={50}>
      <EnhancedFolderTree
        bind:selectedFolderId
        bind:selectedRootType
        bind:selectedNotes
        on:folderSelected={handleFolderSelected}
        on:notesMoved={handleNotesMoved}
      />
    </Pane>
    
    <PaneResizer />
    
    <!-- 筆記列表 -->
    <Pane defaultSize={70}>
      <EnhancedNoteList
        filteredNotes={notes}
        {folders}
        on:notesChanged={handleNotesMoved}
      />
    </Pane>
  </PaneGroup>
</div>
```

## 自定義樣式

### 拖拽狀態樣式

```css
/* 拖拽中的筆記項 */
.note-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

/* 拖拽目標高亮 */
.folder-node.drop-target {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
  border-radius: 6px;
}

/* 選中狀態 */
.note-item.selected {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}
```

### 批量操作工具欄樣式

```css
.batch-toolbar {
  background-color: rgba(59, 130, 246, 0.1);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 12px;
}

.dark .batch-toolbar {
  background-color: rgba(59, 130, 246, 0.2);
  border-bottom-color: rgba(59, 130, 246, 0.3);
}
```

## 事件處理

### 拖拽事件流程

1. **dragstart**: 開始拖拽，設置拖拽數據
2. **dragover**: 拖拽經過目標，設置拖拽效果
3. **dragenter**: 進入拖拽目標，顯示高亮
4. **dragleave**: 離開拖拽目標，移除高亮
5. **drop**: 放下拖拽項，執行移動操作
6. **dragend**: 拖拽結束，清理狀態

### 錯誤處理

```typescript
const handleDrop = async (e: DragEvent) => {
  try {
    const data = JSON.parse(e.dataTransfer.getData('application/json'));
    
    if (selectedNotes.size > 1 && selectedNotes.has(data.noteId)) {
      // 批量移動
      const result = await batchMoveNotesToFolder(
        localStorage.token, 
        Array.from(selectedNotes), 
        folder.id
      );
      
      if (result.failed_notes?.length > 0) {
        toast.warning(`部分筆記移動失敗: ${result.failed_notes.length} 個`);
      } else {
        toast.success(`已移動 ${result.moved_notes.length} 個筆記`);
      }
    } else {
      // 單個移動
      await moveNoteToFolder(localStorage.token, data.noteId, folder.id);
      toast.success(`筆記已移動到 "${folder.name}"`);
    }
    
    dispatch('notesMoved');
  } catch (error) {
    toast.error(`移動失敗: ${error}`);
  }
};
```

### 性能優化

1. **防抖處理**: 拖拽事件使用防抖避免頻繁觸發
2. **虛擬滾動**: 大量筆記時使用虛擬滾動
3. **懶加載**: 資料夾內容按需載入
4. **批量操作**: 優先使用批量 API

```typescript
// 防抖處理拖拽事件
import { debounce } from 'lodash-es';

const debouncedDragOver = debounce((e: DragEvent) => {
  e.preventDefault();
  isDropTarget = true;
}, 100);
```

## 最佳實踐

1. **用戶體驗**: 提供清晰的視覺反饋和狀態提示
2. **錯誤處理**: 優雅處理網路錯誤和權限問題
3. **性能**: 避免不必要的重新渲染和 API 調用
4. **可訪問性**: 支援鍵盤操作和螢幕閱讀器
5. **響應式**: 適配不同螢幕尺寸和設備

## 故障排除

### 常見問題

1. **拖拽不工作**: 檢查 `draggable="true"` 屬性和事件處理器
2. **權限錯誤**: 確認用戶有相應的筆記和資料夾權限
3. **樣式問題**: 檢查 CSS 類名和深色模式適配
4. **性能問題**: 使用瀏覽器開發工具分析性能瓶頸

### 調試技巧

```typescript
// 啟用拖拽調試
const handleDragStart = (e: DragEvent) => {
  console.log('Drag started:', { noteId: note.id, multiSelect: multiSelectMode });
  // ... 其他邏輯
};

const handleDrop = async (e: DragEvent) => {
  console.log('Drop received:', e.dataTransfer?.getData('application/json'));
  // ... 其他邏輯
};
```
